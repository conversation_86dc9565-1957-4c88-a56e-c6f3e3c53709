import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/vehicles/data/repositories/vehicle_repository_impl.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_filter_model.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:geolocator/geolocator.dart';

// Generate mocks
@GenerateMocks([
  NetworkInfo,
  SupabaseClient,
  SupabaseQueryBuilder,
  PostgrestFilterBuilder,
  PostgrestTransformBuilder,
])
import 'vehicle_repository_impl_test.mocks.dart';

void main() {
  late VehicleRepositoryImpl repository;
  late MockNetworkInfo mockNetworkInfo;
  late MockSupabaseClient mockSupabaseClient;
  late MockSupabaseQueryBuilder mockQueryBuilder;
  late MockPostgrestFilterBuilder mockFilterBuilder;
  late MockPostgrestTransformBuilder mockTransformBuilder;

  setUp(() {
    mockNetworkInfo = MockNetworkInfo();
    mockSupabaseClient = MockSupabaseClient();
    mockQueryBuilder = MockSupabaseQueryBuilder();
    mockFilterBuilder = MockPostgrestFilterBuilder();
    mockTransformBuilder = MockPostgrestTransformBuilder();
    
    repository = VehicleRepositoryImpl(
      networkInfo: mockNetworkInfo,
      supabaseClient: mockSupabaseClient,
    );
  });

  group('VehicleRepositoryImpl', () {
    group('getVehicles', () {
      test('should return list of vehicles when network is connected', () async {
        // Arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(mockSupabaseClient.from('vehicles')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq(any, any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.order(any, ascending: anyNamed('ascending')))
            .thenReturn(mockTransformBuilder);
        when(mockTransformBuilder.range(any, any)).thenAnswer((_) async => [
          {
            'id': '1',
            'make': 'Toyota',
            'model': 'Camry',
            'year': 2020,
            'daily_rate': 50.0,
            'is_available': true,
            'type': 'sedan',
            'transmission': 'automatic',
            'fuel_type': 'petrol',
            'seating_capacity': 5,
            'rating': 4.5,
            'total_trips': 10,
            'created_at': '2023-01-01T00:00:00Z',
            'owner': {
              'id': 'owner1',
              'full_name': 'John Doe',
              'avatar_url': null,
              'rating': 4.8,
              'created_at': '2023-01-01T00:00:00Z',
            }
          }
        ]);

        // Act
        final result = await repository.getVehicles();

        // Assert
        expect(result, isA<List<VehicleModel>>());
        expect(result.length, 1);
        expect(result.first.make, 'Toyota');
        expect(result.first.model, 'Camry');
      });

      test('should throw NetworkException when network is not connected', () async {
        // Arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);

        // Act & Assert
        expect(
          () => repository.getVehicles(),
          throwsA(isA<NetworkException>()),
        );
      });
    });

    group('getVehicleById', () {
      test('should return vehicle when found', () async {
        // Arrange
        const vehicleId = '1';
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(mockSupabaseClient.from('vehicles')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('id', vehicleId)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'id': vehicleId,
          'make': 'Toyota',
          'model': 'Camry',
          'year': 2020,
          'daily_rate': 50.0,
          'is_available': true,
          'type': 'sedan',
          'transmission': 'automatic',
          'fuel_type': 'petrol',
          'seating_capacity': 5,
          'rating': 4.5,
          'total_trips': 10,
          'created_at': '2023-01-01T00:00:00Z',
          'owner': {
            'id': 'owner1',
            'full_name': 'John Doe',
            'avatar_url': null,
            'rating': 4.8,
            'created_at': '2023-01-01T00:00:00Z',
          }
        });

        // Act
        final result = await repository.getVehicleById(vehicleId);

        // Assert
        expect(result, isA<VehicleModel>());
        expect(result?.id, vehicleId);
        expect(result?.make, 'Toyota');
      });

      test('should return null when vehicle not found', () async {
        // Arrange
        const vehicleId = 'nonexistent';
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(mockSupabaseClient.from('vehicles')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('id', vehicleId)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenThrow(
          PostgrestException(message: 'Not found', code: 'PGRST116'),
        );

        // Act
        final result = await repository.getVehicleById(vehicleId);

        // Assert
        expect(result, isNull);
      });
    });

    group('getVehiclesAdvanced', () {
      test('should return filtered vehicles', () async {
        // Arrange
        final filter = VehicleFilterModel(
          vehicleType: VehicleType.sedan,
          maxDailyRate: 100.0,
          limit: 10,
          offset: 0,
        );
        
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(mockSupabaseClient.from('vehicles')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq(any, any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.lte(any, any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.order(any, ascending: anyNamed('ascending')))
            .thenReturn(mockTransformBuilder);
        when(mockTransformBuilder.range(any, any)).thenAnswer((_) async => [
          {
            'id': '1',
            'make': 'Toyota',
            'model': 'Camry',
            'year': 2020,
            'daily_rate': 50.0,
            'is_available': true,
            'type': 'sedan',
            'transmission': 'automatic',
            'fuel_type': 'petrol',
            'seating_capacity': 5,
            'rating': 4.5,
            'total_trips': 10,
            'created_at': '2023-01-01T00:00:00Z',
            'profiles': {
              'id': 'owner1',
              'full_name': 'John Doe',
              'avatar_url': null,
              'rating': 4.8,
              'created_at': '2023-01-01T00:00:00Z',
            }
          }
        ]);

        // Act
        final result = await repository.getVehiclesAdvanced(filter);

        // Assert
        expect(result, isA<List<VehicleModel>>());
        expect(result.length, 1);
        expect(result.first.type, VehicleType.sedan);
      });
    });

    group('createVehicle', () {
      test('should create vehicle successfully', () async {
        // Arrange
        final vehicle = VehicleModel(
          id: '1',
          make: 'Toyota',
          model: 'Camry',
          year: 2020,
          dailyRate: 50.0,
          isAvailable: true,
          type: VehicleType.sedan,
          transmission: TransmissionType.automatic,
          fuelType: FuelType.petrol,
          seatingCapacity: 5,
          ownerId: 'owner1',
          rating: 0.0,
          totalTrips: 0,
          createdAt: DateTime.now(),
        );

        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(mockSupabaseClient.from('vehicles')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.insert(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.select(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'id': '1',
          'make': 'Toyota',
          'model': 'Camry',
          'year': 2020,
          'daily_rate': 50.0,
          'is_available': true,
          'type': 'sedan',
          'transmission': 'automatic',
          'fuel_type': 'petrol',
          'seating_capacity': 5,
          'rating': 0.0,
          'total_trips': 0,
          'owner_id': 'owner1',
          'created_at': '2023-01-01T00:00:00Z',
          'profiles': {
            'id': 'owner1',
            'full_name': 'John Doe',
            'avatar_url': null,
            'rating': 4.8,
            'created_at': '2023-01-01T00:00:00Z',
          }
        });

        // Act
        final result = await repository.createVehicle(vehicle);

        // Assert
        expect(result, isA<VehicleModel>());
        expect(result.make, 'Toyota');
        expect(result.model, 'Camry');
      });
    });

    group('getAvailableVehicleMakes', () {
      test('should return list of unique makes', () async {
        // Arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(mockSupabaseClient.from('vehicles')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select('make')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('is_available', true)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.order('make')).thenAnswer((_) async => [
          {'make': 'Toyota'},
          {'make': 'Honda'},
          {'make': 'Toyota'}, // Duplicate
          {'make': 'Nissan'},
        ]);

        // Act
        final result = await repository.getAvailableVehicleMakes();

        // Assert
        expect(result, isA<List<String>>());
        expect(result.length, 3); // Should remove duplicates
        expect(result, contains('Toyota'));
        expect(result, contains('Honda'));
        expect(result, contains('Nissan'));
      });
    });

    group('getAvailableVehicleFeatures', () {
      test('should return list of features', () async {
        // Act
        final result = await repository.getAvailableVehicleFeatures();

        // Assert
        expect(result, isA<List<String>>());
        expect(result, isNotEmpty);
        expect(result, contains('Air Conditioning'));
        expect(result, contains('Bluetooth'));
      });
    });
  });
}
