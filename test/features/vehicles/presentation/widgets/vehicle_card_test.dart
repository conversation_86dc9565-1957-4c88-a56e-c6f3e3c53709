import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_card.dart';

void main() {
  group('VehicleCard Widget Tests', () {
    late VehicleModel testVehicle;

    setUp(() {
      testVehicle = VehicleModel(
        id: 'test_id',
        ownerId: 'owner_123',
        make: 'Toyota',
        model: 'Corolla',
        year: 2020,
        color: 'White',
        licensePlate: 'BA 1 PA 1234',
        dailyRate: 2000.0,
        location: 'Kathmandu',
        latitude: 27.7172,
        longitude: 85.3240,
        description: 'Test vehicle description',
        features: ['AC', 'GPS', 'Bluetooth'],
        images: ['https://example.com/image1.jpg'],
        isAvailable: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    Widget createWidgetUnderTest() {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: VehicleCard(vehicle: testVehicle),
          ),
        ),
      );
    }

    testWidgets('should display vehicle make and model', (tester) async {
      // arrange
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.text('Toyota Corolla'), findsOneWidget);
    });

    testWidgets('should display vehicle year', (tester) async {
      // arrange
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.text('2020'), findsOneWidget);
    });

    testWidgets('should display daily rate', (tester) async {
      // arrange
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.text('NPR 2,000/day'), findsOneWidget);
    });

    testWidgets('should display location', (tester) async {
      // arrange
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.text('Kathmandu'), findsOneWidget);
    });

    testWidgets('should display availability status when available', (tester) async {
      // arrange
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.text('Available'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('should display unavailable status when not available', (tester) async {
      // arrange
      testVehicle = testVehicle.copyWith(isAvailable: false);
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.text('Not Available'), findsOneWidget);
      expect(find.byIcon(Icons.cancel), findsOneWidget);
    });

    testWidgets('should display vehicle features', (tester) async {
      // arrange
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.text('AC'), findsOneWidget);
      expect(find.text('GPS'), findsOneWidget);
      expect(find.text('Bluetooth'), findsOneWidget);
    });

    testWidgets('should be tappable', (tester) async {
      // arrange
      bool wasTapped = false;
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: VehicleCard(
                vehicle: testVehicle,
                onTap: () => wasTapped = true,
              ),
            ),
          ),
        ),
      );

      // act
      await tester.tap(find.byType(VehicleCard));
      await tester.pump();

      // assert
      expect(wasTapped, isTrue);
    });

    testWidgets('should display vehicle image', (tester) async {
      // arrange
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.byType(Image), findsOneWidget);
    });

    testWidgets('should handle empty features list', (tester) async {
      // arrange
      testVehicle = testVehicle.copyWith(features: []);
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.text('No features listed'), findsOneWidget);
    });

    testWidgets('should display placeholder when no image available', (tester) async {
      // arrange
      testVehicle = testVehicle.copyWith(images: []);
      await tester.pumpWidget(createWidgetUnderTest());

      // act & assert
      expect(find.byIcon(Icons.directions_car), findsOneWidget);
    });

    testWidgets('should apply correct styling for available vehicle', (tester) async {
      // arrange
      await tester.pumpWidget(createWidgetUnderTest());

      // act
      final cardFinder = find.byType(Card);
      final card = tester.widget<Card>(cardFinder);

      // assert
      expect(card.elevation, greaterThan(0));
      expect(cardFinder, findsOneWidget);
    });

    testWidgets('should apply different styling for unavailable vehicle', (tester) async {
      // arrange
      testVehicle = testVehicle.copyWith(isAvailable: false);
      await tester.pumpWidget(createWidgetUnderTest());

      // act
      final cardFinder = find.byType(Card);

      // assert
      expect(cardFinder, findsOneWidget);
      // Card should still be present but with different visual treatment
    });
  });
}
