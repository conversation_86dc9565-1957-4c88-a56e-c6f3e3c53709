import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/payments/data/repositories/payment_repository_impl.dart';
import 'package:gaadi_sewa/features/payments/data/services/khalti_payment_service.dart';
import 'package:gaadi_sewa/features/payments/data/models/payment_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class MockNetworkInfo extends Mock implements NetworkInfo {}
class MockKhaltiPaymentService extends Mock implements KhaltiPaymentService {}
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockPostgrestFilterBuilder extends Mock implements PostgrestFilterBuilder {}

void main() {
  late PaymentRepositoryImpl repository;
  late MockNetworkInfo mockNetworkInfo;
  late MockKhaltiPaymentService mockKhaltiService;
  late MockSupabaseClient mockSupabaseClient;

  setUp(() {
    mockNetworkInfo = MockNetworkInfo();
    mockKhaltiService = MockKhaltiPaymentService();
    mockSupabaseClient = MockSupabaseClient();
    repository = PaymentRepositoryImpl(
      networkInfo: mockNetworkInfo,
      khaltiService: mockKhaltiService,
      supabaseClient: mockSupabaseClient,
    );
  });

  group('PaymentRepository', () {
    const tBookingId = 'booking_123';
    const tAmount = 1000.0;
    const tPaymentMethod = 'khalti';
    const tPaymentId = 'payment_123';

    final tPaymentModel = PaymentModel(
      id: tPaymentId,
      bookingId: tBookingId,
      amount: tAmount,
      currency: 'NPR',
      status: PaymentStatus.completed,
      paymentMethod: PaymentMethod.khalti,
      transactionId: 'txn_123',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      metadata: {'test': 'data'},
    );

    test('should check if device is online before processing payment', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);

      // act
      final result = await repository.processPayment(
        bookingId: tBookingId,
        amount: tAmount,
        paymentMethod: tPaymentMethod,
      );

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(result, equals(Left(NetworkFailure(message: 'No internet connection'))));
    });

    test('should process payment successfully when device is online', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockKhaltiService.initiatePayment(
        productIdentity: any(named: 'productIdentity'),
        productName: any(named: 'productName'),
        amount: any(named: 'amount'),
        productUrl: any(named: 'productUrl'),
        customerInfo: any(named: 'customerInfo'),
      )).thenAnswer((_) async => {
        'pidx': tPaymentId,
        'amount': 100000, // in paisa
        'status': 'completed',
        'transaction_id': 'txn_123',
      });

      when(() => mockKhaltiService.convertToPaymentModel(
        khaltiResponse: any(named: 'khaltiResponse'),
        bookingId: any(named: 'bookingId'),
        userId: any(named: 'userId'),
      )).thenReturn(tPaymentModel);

      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('payments')).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.insert(any())).thenReturn(mockFilterBuilder);

      // act
      final result = await repository.processPayment(
        bookingId: tBookingId,
        amount: tAmount,
        paymentMethod: tPaymentMethod,
        metadata: {'user_id': 'user_123'},
      );

      // assert
      verify(() => mockNetworkInfo.isConnected);
      verify(() => mockKhaltiService.initiatePayment(
        productIdentity: any(named: 'productIdentity'),
        productName: any(named: 'productName'),
        amount: any(named: 'amount'),
        productUrl: any(named: 'productUrl'),
        customerInfo: any(named: 'customerInfo'),
      ));
      expect(result, equals(Right(tPaymentModel)));
    });

    test('should return server failure when Khalti service throws exception', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockKhaltiService.initiatePayment(
        productIdentity: any(named: 'productIdentity'),
        productName: any(named: 'productName'),
        amount: any(named: 'amount'),
        productUrl: any(named: 'productUrl'),
        customerInfo: any(named: 'customerInfo'),
      )).thenThrow(Exception('Khalti error'));

      // act
      final result = await repository.processPayment(
        bookingId: tBookingId,
        amount: tAmount,
        paymentMethod: tPaymentMethod,
      );

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(result, isA<Left>());
    });

    test('should get payment by ID successfully', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      
      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('payments')).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.select()).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.eq('id', tPaymentId)).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.single()).thenAnswer((_) async => tPaymentModel.toJson());

      // act
      final result = await repository.getPayment(tPaymentId);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(result, isA<Right>());
    });

    test('should verify payment successfully', () async {
      // arrange
      const tTransactionId = 'txn_123';
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      
      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('payments')).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.select()).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.eq('id', tPaymentId)).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.single()).thenAnswer((_) async => tPaymentModel.toJson());

      when(() => mockKhaltiService.verifyPayment(
        token: tTransactionId,
        amount: any(named: 'amount'),
      )).thenAnswer((_) async => {
        'state': {'name': 'Completed'},
        'transaction_id': tTransactionId,
      });

      // act
      final result = await repository.verifyPayment(tPaymentId, tTransactionId);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      verify(() => mockKhaltiService.verifyPayment(
        token: tTransactionId,
        amount: any(named: 'amount'),
      ));
      expect(result, equals(const Right(true)));
    });
  });
}
