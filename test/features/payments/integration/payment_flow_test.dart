import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/payments/presentation/screens/payment_screen.dart';
import 'package:gaadi_sewa/features/payments/presentation/providers/payment_provider.dart';
import 'package:gaadi_sewa/features/payments/data/models/payment_model.dart';
import 'package:gaadi_sewa/features/payments/domain/repositories/payment_repository.dart';

// Mock classes
class MockPaymentRepository extends Mock implements PaymentRepository {}

void main() {
  group('Payment Flow Integration Tests', () {
    late MockPaymentRepository mockPaymentRepository;
    late BookingModel testBooking;
    late VehicleModel testVehicle;

    setUp(() {
      mockPaymentRepository = MockPaymentRepository();
      
      // Create test vehicle
      testVehicle = VehicleModel(
        id: 'test-vehicle-id',
        make: 'Toyota',
        model: 'Corolla',
        year: 2020,
        dailyRate: 5000.0,
        location: 'Kathmandu',
        latitude: 27.7172,
        longitude: 85.3240,
        description: 'Test vehicle',
        features: ['AC', 'GPS'],
        images: ['test-image.jpg'],
        isAvailable: true,
        ownerId: 'test-owner-id',
        ownerName: 'Test Owner',
        ownerPhone: '+977-9800000000',
        createdAt: DateTime.now(),
        category: VehicleCategory.car,
        transmission: TransmissionType.manual,
        fuelType: FuelType.petrol,
        seats: 5,
      );

      // Create test booking
      testBooking = BookingModel(
        id: 'test-booking-id',
        userId: 'test-user-id',
        vehicleId: testVehicle.id,
        vehicle: testVehicle,
        startDate: DateTime.now().add(const Duration(days: 1)),
        endDate: DateTime.now().add(const Duration(days: 3)),
        totalAmount: 10000.0,
        status: BookingStatus.pending,
        createdAt: DateTime.now(),
      );

      // Register fallback values for mocktail
      registerFallbackValue(testBooking);
    });

    testWidgets('Payment screen displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            paymentRepositoryProvider.overrideWithValue(mockPaymentRepository),
          ],
          child: MaterialApp(
            home: PaymentScreen(booking: testBooking),
          ),
        ),
      );

      // Verify payment screen elements
      expect(find.text('Payment'), findsOneWidget);
      expect(find.text('Booking Summary'), findsOneWidget);
      expect(find.text('Toyota Corolla'), findsOneWidget);
      expect(find.text('NPR 10000.00'), findsOneWidget);
    });

    testWidgets('Khalti payment widget appears when Khalti is selected', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            paymentRepositoryProvider.overrideWithValue(mockPaymentRepository),
          ],
          child: MaterialApp(
            home: PaymentScreen(booking: testBooking),
          ),
        ),
      );

      // Find and tap Khalti payment method
      final khaltiOption = find.text('Khalti');
      expect(khaltiOption, findsOneWidget);
      
      await tester.tap(khaltiOption);
      await tester.pumpAndSettle();

      // Verify Khalti payment widget appears
      expect(find.text('Pay with Khalti'), findsOneWidget);
      expect(find.text('Digital Wallet Payment'), findsOneWidget);
    });

    testWidgets('Payment processing shows loading state', (WidgetTester tester) async {
      // Mock successful payment
      when(() => mockPaymentRepository.processPayment(
        bookingId: any(named: 'bookingId'),
        amount: any(named: 'amount'),
        paymentMethod: any(named: 'paymentMethod'),
        metadata: any(named: 'metadata'),
      )).thenAnswer((_) async => Right(PaymentModel(
        id: 'test-payment-id',
        bookingId: testBooking.id,
        amount: testBooking.totalAmount,
        paymentMethod: PaymentMethod.khalti,
        status: PaymentStatus.completed,
        createdAt: DateTime.now(),
      )));

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            paymentRepositoryProvider.overrideWithValue(mockPaymentRepository),
          ],
          child: MaterialApp(
            home: PaymentScreen(booking: testBooking),
          ),
        ),
      );

      // Select Khalti and initiate payment
      await tester.tap(find.text('Khalti'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Pay with Khalti'));
      await tester.pump();

      // Verify loading state
      expect(find.text('Processing...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Payment success flow works correctly', (WidgetTester tester) async {
      // Mock successful payment
      when(() => mockPaymentRepository.processPayment(
        bookingId: any(named: 'bookingId'),
        amount: any(named: 'amount'),
        paymentMethod: any(named: 'paymentMethod'),
        metadata: any(named: 'metadata'),
      )).thenAnswer((_) async => Right(PaymentModel(
        id: 'test-payment-id',
        bookingId: testBooking.id,
        amount: testBooking.totalAmount,
        paymentMethod: PaymentMethod.khalti,
        status: PaymentStatus.completed,
        createdAt: DateTime.now(),
      )));

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            paymentRepositoryProvider.overrideWithValue(mockPaymentRepository),
          ],
          child: MaterialApp(
            home: PaymentScreen(booking: testBooking),
            routes: {
              '/payment/success': (context) => const Scaffold(
                body: Center(child: Text('Payment Success')),
              ),
            },
          ),
        ),
      );

      // Select Khalti and complete payment
      await tester.tap(find.text('Khalti'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Pay with Khalti'));
      await tester.pumpAndSettle();

      // Simulate payment completion in the dialog
      await tester.tap(find.text('Complete Payment'));
      await tester.pumpAndSettle();

      // Verify navigation to success screen
      expect(find.text('Payment Success'), findsOneWidget);
    });

    testWidgets('Payment failure shows error message', (WidgetTester tester) async {
      // Mock payment failure
      when(() => mockPaymentRepository.processPayment(
        bookingId: any(named: 'bookingId'),
        amount: any(named: 'amount'),
        paymentMethod: any(named: 'paymentMethod'),
        metadata: any(named: 'metadata'),
      )).thenAnswer((_) async => Left(ServerFailure(message: 'Payment failed')));

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            paymentRepositoryProvider.overrideWithValue(mockPaymentRepository),
          ],
          child: MaterialApp(
            home: PaymentScreen(booking: testBooking),
          ),
        ),
      );

      // Select Khalti and attempt payment
      await tester.tap(find.text('Khalti'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Pay with Khalti'));
      await tester.pumpAndSettle();

      // Verify error message appears
      expect(find.text('Payment failed'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    group('Payment Repository Tests', () {
      test('processPayment calls Khalti service correctly', () async {
        // This would test the actual repository implementation
        // For now, we'll verify the mock was called correctly
        when(() => mockPaymentRepository.processPayment(
          bookingId: testBooking.id,
          amount: testBooking.totalAmount,
          paymentMethod: 'khalti',
          metadata: any(named: 'metadata'),
        )).thenAnswer((_) async => Right(PaymentModel(
          id: 'test-payment-id',
          bookingId: testBooking.id,
          amount: testBooking.totalAmount,
          paymentMethod: PaymentMethod.khalti,
          status: PaymentStatus.pending,
          createdAt: DateTime.now(),
        )));

        final result = await mockPaymentRepository.processPayment(
          bookingId: testBooking.id,
          amount: testBooking.totalAmount,
          paymentMethod: 'khalti',
          metadata: {
            'vehicle_name': '${testVehicle.make} ${testVehicle.model}',
            'booking_dates': '${testBooking.startDate.toIso8601String()} - ${testBooking.endDate.toIso8601String()}',
          },
        );

        expect(result.isRight(), true);
        verify(() => mockPaymentRepository.processPayment(
          bookingId: testBooking.id,
          amount: testBooking.totalAmount,
          paymentMethod: 'khalti',
          metadata: any(named: 'metadata'),
        )).called(1);
      });
    });
  });
}
