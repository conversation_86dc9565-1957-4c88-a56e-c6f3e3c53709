import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/presentation/widgets/app_error_widget.dart';
import 'package:gaadi_sewa/core/presentation/widgets/app_loading_indicator.dart';
import 'package:gaadi_sewa/features/messaging/domain/models/conversation_model.dart';
import 'package:gaadi_sewa/features/messaging/presentation/providers/messaging_providers.dart';
import 'package:gaadi_sewa/features/messaging/presentation/screens/conversation_screen.dart';
import 'package:gaadi_sewa/features/messaging/presentation/widgets/conversation_list_item.dart';
import 'package:gaadi_sewa/features/user/presentation/providers/auth_provider.dart';

class ConversationListScreen extends ConsumerWidget {
  static const String routeName = '/conversations';

  const ConversationListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);
    final conversationsAsync = ref.watch(userConversationsProvider(currentUser?.id ?? ''));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Messages'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Implement search functionality
            },
          ),
        ],
      ),
      body: conversationsAsync.when(
        data: (conversations) {
          if (conversations.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.message_outlined,
                    size: 80,
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No conversations yet',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Start a conversation by messaging a vehicle owner',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }
          
          return ListView.separated(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: conversations.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final conversation = conversations[index];
              return ConversationListItem(
                conversation: conversation,
                onTap: () => _navigateToConversation(context, conversation),
              );
            },
          );
        },
        loading: () => const AppLoadingIndicator(),
        error: (error, stackTrace) => AppErrorWidget(
          message: 'Failed to load conversations: $error',
          onRetry: () => ref.refresh(userConversationsProvider(currentUser?.id ?? '')),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Implement new conversation functionality
        },
        child: const Icon(Icons.add_comment),
      ),
    );
  }

  void _navigateToConversation(BuildContext context, ConversationModel conversation) {
    Navigator.pushNamed(
      context,
      ConversationScreen.routeName,
      arguments: conversation.id,
    );
  }
}
