// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VehicleLocation _$VehicleLocationFromJson(Map<String, dynamic> json) =>
    VehicleLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      postalCode: json['postalCode'] as String?,
    );

Map<String, dynamic> _$VehicleLocationToJson(VehicleLocation instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'postalCode': instance.postalCode,
    };

VehicleModel _$VehicleModelFromJson(Map<String, dynamic> json) => VehicleModel(
      id: json['id'] as String,
      ownerId: json['ownerId'] as String,
      type: $enumDecode(_$VehicleTypeEnumMap, json['type']),
      make: json['make'] as String,
      model: json['model'] as String,
      year: (json['year'] as num).toInt(),
      licensePlate: json['licensePlate'] as String,
      color: json['color'] as String,
      transmission:
          $enumDecode(_$TransmissionTypeEnumMap, json['transmission']),
      fuelType: $enumDecode(_$FuelTypeEnumMap, json['fuelType']),
      seatingCapacity: (json['seatingCapacity'] as num).toInt(),
      dailyRate: (json['dailyRate'] as num).toDouble(),
      hourlyRate: (json['hourlyRate'] as num?)?.toDouble(),
      weeklyDiscount: (json['weeklyDiscount'] as num?)?.toDouble(),
      monthlyDiscount: (json['monthlyDiscount'] as num?)?.toDouble(),
      isAvailable: json['isAvailable'] as bool? ?? true,
      description: json['description'] as String?,
      rules: json['rules'] as String?,
      location: json['location'] == null
          ? null
          : VehicleLocation.fromJson(json['location'] as Map<String, dynamic>),
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      insuranceProvider: json['insuranceProvider'] as String?,
      insurancePolicyNumber: json['insurancePolicyNumber'] as String?,
      insuranceExpiryDate: json['insuranceExpiryDate'] == null
          ? null
          : DateTime.parse(json['insuranceExpiryDate'] as String),
      inspectionDate: json['inspectionDate'] == null
          ? null
          : DateTime.parse(json['inspectionDate'] as String),
      mileage: (json['mileage'] as num?)?.toInt(),
      engineSize: (json['engineSize'] as num?)?.toDouble(),
      fuelConsumption: (json['fuelConsumption'] as num?)?.toDouble(),
      doors: (json['doors'] as num?)?.toInt(),
      airConditioning: json['airConditioning'] as bool? ?? false,
      bluetooth: json['bluetooth'] as bool? ?? false,
      gps: json['gps'] as bool? ?? false,
      sunroof: json['sunroof'] as bool? ?? false,
      usbPorts: json['usbPorts'] as bool? ?? false,
      backupCamera: json['backupCamera'] as bool? ?? false,
      parkingSensors: json['parkingSensors'] as bool? ?? false,
      childSeat: json['childSeat'] as bool? ?? false,
      allWheelDrive: json['allWheelDrive'] as bool? ?? false,
      convertible: json['convertible'] as bool? ?? false,
      manualTransmission: json['manualTransmission'] as bool? ?? false,
      petFriendly: json['petFriendly'] as bool? ?? false,
      smokingAllowed: json['smokingAllowed'] as bool? ?? false,
      tollPass: json['tollPass'] as bool? ?? false,
      unlimitedMileage: json['unlimitedMileage'] as bool? ?? false,
      delivery: json['delivery'] as bool? ?? false,
      pickupDropoff: json['pickupDropoff'] as bool? ?? false,
      roadsideAssistance: json['roadsideAssistance'] as bool? ?? false,
      additionalDriver: json['additionalDriver'] as bool? ?? false,
      youngDriverSurcharge: json['youngDriverSurcharge'] as bool? ?? false,
      seniorDriverSurcharge: json['seniorDriverSurcharge'] as bool? ?? false,
      airportFee: (json['airportFee'] as num?)?.toDouble(),
      cleaningFee: (json['cleaningFee'] as num?)?.toDouble(),
      deposit: (json['deposit'] as num?)?.toDouble(),
      excess: (json['excess'] as num?)?.toDouble(),
      theftProtection: json['theftProtection'] as bool? ?? false,
      tireProtection: json['tireProtection'] as bool? ?? false,
      windshieldProtection: json['windshieldProtection'] as bool? ?? false,
      additionalInsurance: json['additionalInsurance'] as bool? ?? false,
      cancellationPolicy: json['cancellationPolicy'] as String?,
      minimumRentalDays: (json['minimumRentalDays'] as num?)?.toInt(),
      maximumRentalDays: (json['maximumRentalDays'] as num?)?.toInt(),
      advanceNoticeHours: (json['advanceNoticeHours'] as num?)?.toInt(),
      lastRentedAt: json['lastRentedAt'] == null
          ? null
          : DateTime.parse(json['lastRentedAt'] as String),
      nextAvailableDate: json['nextAvailableDate'] == null
          ? null
          : DateTime.parse(json['nextAvailableDate'] as String),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalRatings: (json['totalRatings'] as num?)?.toInt() ?? 0,
      totalTrips: (json['totalTrips'] as num?)?.toInt() ?? 0,
      isFeatured: json['isFeatured'] as bool? ?? false,
      isVerified: json['isVerified'] as bool? ?? false,
      verificationStatus: $enumDecodeNullable(
          _$VerificationStatusEnumMap, json['verificationStatus']),
      rejectionReason: json['rejectionReason'] as String?,
      status: $enumDecodeNullable(_$VehicleStatusEnumMap, json['status']),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$VehicleModelToJson(VehicleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'ownerId': instance.ownerId,
      'type': _$VehicleTypeEnumMap[instance.type]!,
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'licensePlate': instance.licensePlate,
      'color': instance.color,
      'transmission': _$TransmissionTypeEnumMap[instance.transmission]!,
      'fuelType': _$FuelTypeEnumMap[instance.fuelType]!,
      'seatingCapacity': instance.seatingCapacity,
      'dailyRate': instance.dailyRate,
      'hourlyRate': instance.hourlyRate,
      'weeklyDiscount': instance.weeklyDiscount,
      'monthlyDiscount': instance.monthlyDiscount,
      'isAvailable': instance.isAvailable,
      'description': instance.description,
      'rules': instance.rules,
      'location': instance.location,
      'features': instance.features,
      'images': instance.images,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'insuranceProvider': instance.insuranceProvider,
      'insurancePolicyNumber': instance.insurancePolicyNumber,
      'insuranceExpiryDate': instance.insuranceExpiryDate?.toIso8601String(),
      'inspectionDate': instance.inspectionDate?.toIso8601String(),
      'mileage': instance.mileage,
      'engineSize': instance.engineSize,
      'fuelConsumption': instance.fuelConsumption,
      'doors': instance.doors,
      'airConditioning': instance.airConditioning,
      'bluetooth': instance.bluetooth,
      'gps': instance.gps,
      'sunroof': instance.sunroof,
      'usbPorts': instance.usbPorts,
      'backupCamera': instance.backupCamera,
      'parkingSensors': instance.parkingSensors,
      'childSeat': instance.childSeat,
      'allWheelDrive': instance.allWheelDrive,
      'convertible': instance.convertible,
      'manualTransmission': instance.manualTransmission,
      'petFriendly': instance.petFriendly,
      'smokingAllowed': instance.smokingAllowed,
      'tollPass': instance.tollPass,
      'unlimitedMileage': instance.unlimitedMileage,
      'delivery': instance.delivery,
      'pickupDropoff': instance.pickupDropoff,
      'roadsideAssistance': instance.roadsideAssistance,
      'additionalDriver': instance.additionalDriver,
      'youngDriverSurcharge': instance.youngDriverSurcharge,
      'seniorDriverSurcharge': instance.seniorDriverSurcharge,
      'airportFee': instance.airportFee,
      'cleaningFee': instance.cleaningFee,
      'deposit': instance.deposit,
      'excess': instance.excess,
      'theftProtection': instance.theftProtection,
      'tireProtection': instance.tireProtection,
      'windshieldProtection': instance.windshieldProtection,
      'additionalInsurance': instance.additionalInsurance,
      'cancellationPolicy': instance.cancellationPolicy,
      'minimumRentalDays': instance.minimumRentalDays,
      'maximumRentalDays': instance.maximumRentalDays,
      'advanceNoticeHours': instance.advanceNoticeHours,
      'lastRentedAt': instance.lastRentedAt?.toIso8601String(),
      'nextAvailableDate': instance.nextAvailableDate?.toIso8601String(),
      'rating': instance.rating,
      'totalRatings': instance.totalRatings,
      'totalTrips': instance.totalTrips,
      'isFeatured': instance.isFeatured,
      'isVerified': instance.isVerified,
      'verificationStatus':
          _$VerificationStatusEnumMap[instance.verificationStatus],
      'rejectionReason': instance.rejectionReason,
      'status': _$VehicleStatusEnumMap[instance.status],
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$VehicleTypeEnumMap = {
  VehicleType.bicycle: 'bicycle',
  VehicleType.scooter: 'scooter',
  VehicleType.car: 'car',
};

const _$TransmissionTypeEnumMap = {
  TransmissionType.manual: 'manual',
  TransmissionType.automatic: 'automatic',
  TransmissionType.cvt: 'cvt',
  TransmissionType.semiAutomatic: 'semiAutomatic',
};

const _$FuelTypeEnumMap = {
  FuelType.petrol: 'petrol',
  FuelType.diesel: 'diesel',
  FuelType.electric: 'electric',
  FuelType.hybrid: 'hybrid',
  FuelType.cng: 'cng',
  FuelType.lpg: 'lpg',
};

const _$VerificationStatusEnumMap = {
  VerificationStatus.pending: 'pending',
  VerificationStatus.verified: 'verified',
  VerificationStatus.rejected: 'rejected',
};

const _$VehicleStatusEnumMap = {
  VehicleStatus.active: 'active',
  VehicleStatus.inactive: 'inactive',
  VehicleStatus.maintenance: 'maintenance',
  VehicleStatus.pending: 'pending',
  VehicleStatus.rented: 'rented',
};
