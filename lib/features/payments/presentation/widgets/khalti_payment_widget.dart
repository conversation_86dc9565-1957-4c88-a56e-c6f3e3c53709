import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/payments/presentation/providers/payment_provider.dart';
import 'package:url_launcher/url_launcher.dart';

class KhaltiPaymentWidget extends ConsumerStatefulWidget {
  final BookingModel booking;
  final VoidCallback onSuccess;
  final Function(String) onFailure;

  const KhaltiPaymentWidget({
    Key? key,
    required this.booking,
    required this.onSuccess,
    required this.onFailure,
  }) : super(key: key);

  @override
  ConsumerState<KhaltiPaymentWidget> createState() => _KhaltiPaymentWidgetState();
}

class _KhaltiPaymentWidgetState extends ConsumerState<KhaltiPaymentWidget> {
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    final paymentState = ref.watch(paymentNotifierProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Khalti Logo and Info
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF5C2D91),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.payment,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Khalti',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF5C2D91),
                      ),
                    ),
                    Text(
                      'Digital Wallet Payment',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Payment Details
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                _buildDetailRow('Vehicle', '${widget.booking.vehicle.make} ${widget.booking.vehicle.model}'),
                const SizedBox(height: 8),
                _buildDetailRow('Amount', 'NPR ${widget.booking.totalAmount.toStringAsFixed(2)}'),
                const SizedBox(height: 8),
                _buildDetailRow('Booking ID', widget.booking.id.substring(0, 8).toUpperCase()),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Payment Instructions
          const Text(
            'Payment Instructions:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '1. Click "Pay with Khalti" button below\n'
            '2. You will be redirected to Khalti payment page\n'
            '3. Enter your Khalti PIN or use mobile banking\n'
            '4. Complete the payment to confirm your booking',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              height: 1.4,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Payment Button
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: _isProcessing || paymentState.isLoading ? null : _initiatePayment,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5C2D91),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isProcessing || paymentState.isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Processing...'),
                      ],
                    )
                  : const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.security, size: 20),
                        SizedBox(width: 8),
                        Text(
                          'Pay with Khalti',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
          
          // Error Display
          if (paymentState.error != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      paymentState.error!,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: 12),
          
          // Security Notice
          Row(
            children: [
              Icon(Icons.lock, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Your payment is secured by Khalti\'s 256-bit SSL encryption',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Future<void> _initiatePayment() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // Process payment through the provider
      await ref.read(paymentNotifierProvider.notifier).processPayment(
        bookingId: widget.booking.id,
        amount: widget.booking.totalAmount,
        paymentMethod: 'khalti',
        metadata: {
          'vehicle_name': '${widget.booking.vehicle.make} ${widget.booking.vehicle.model}',
          'booking_dates': '${widget.booking.startDate.toIso8601String()} - ${widget.booking.endDate.toIso8601String()}',
          'customer_info': {
            'name': widget.booking.vehicle.ownerName ?? 'Customer',
            'email': '<EMAIL>',
          },
        },
      );

      final paymentState = ref.read(paymentNotifierProvider);
      
      if (paymentState.payment != null) {
        // For now, we'll simulate the Khalti payment flow
        // In a real implementation, you would redirect to Khalti's payment page
        await _simulateKhaltiPayment();
      } else if (paymentState.error != null) {
        widget.onFailure(paymentState.error!);
      }
    } catch (e) {
      widget.onFailure(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<void> _simulateKhaltiPayment() async {
    // Show payment confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Khalti Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.payment,
              size: 48,
              color: Color(0xFF5C2D91),
            ),
            const SizedBox(height: 16),
            Text(
              'Amount: NPR ${widget.booking.totalAmount.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'In a real implementation, you would be redirected to Khalti\'s secure payment page.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            const Text(
              'Simulate payment completion?',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF5C2D91),
              foregroundColor: Colors.white,
            ),
            child: const Text('Complete Payment'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Simulate payment processing delay
      await Future.delayed(const Duration(seconds: 2));
      widget.onSuccess();
    } else {
      widget.onFailure('Payment cancelled by user');
    }
  }
}
