import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/payments/data/models/payment_model.dart';
import 'package:gaadi_sewa/features/payments/domain/repositories/payment_repository.dart';
import 'package:gaadi_sewa/features/payments/domain/usecases/process_payment.dart';

// Provider for Payment Repository
final paymentRepositoryProvider = Provider<PaymentRepository>((ref) {
  throw UnimplementedError('PaymentRepository not initialized');
});

// Provider for ProcessPayment use case
final processPaymentProvider = Provider<ProcessPayment>((ref) {
  final repository = ref.watch(paymentRepositoryProvider);
  return ProcessPayment(repository);
});

// State for payment processing
class PaymentState {
  final bool isLoading;
  final PaymentModel? payment;
  final String? error;

  PaymentState({
    this.isLoading = false,
    this.payment,
    this.error,
  });

  PaymentState copyWith({
    bool? isLoading,
    PaymentModel? payment,
    String? error,
  }) {
    return PaymentState(
      isLoading: isLoading ?? this.isLoading,
      payment: payment ?? this.payment,
      error: error ?? this.error,
    );
  }
}

// Notifier for payment state
class PaymentNotifier extends StateNotifier<PaymentState> {
  final ProcessPayment _processPayment;

  PaymentNotifier(this._processPayment) : super(PaymentState());

  // Process a new payment
  Future<void> processPayment({
    required String bookingId,
    required double amount,
    required String paymentMethod,
    Map<String, dynamic>? metadata,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _processPayment(ProcessPaymentParams(
      bookingId: bookingId,
      amount: amount,
      paymentMethod: paymentMethod,
      metadata: metadata,
    ));

    state = result.fold(
      (failure) => state.copyWith(
        isLoading: false,
        error: failure.message ?? 'Failed to process payment',
      ),
      (payment) => state.copyWith(
        isLoading: false,
        payment: payment,
      ),
    );
  }

  // Reset the payment state
  void reset() {
    state = PaymentState();
  }
}

// Provider for PaymentNotifier
final paymentNotifierProvider =
    StateNotifierProvider<PaymentNotifier, PaymentState>((ref) {
  final processPayment = ref.watch(processPaymentProvider);
  return PaymentNotifier(processPayment);
});

// Provider for payment methods
final paymentMethodsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  return [
    {
      'id': 'khalti',
      'name': 'Khalti',
      'icon': 'assets/icons/khalti.png',
      'color': const Color(0xFF5C2D91),
    },
    {
      'id': 'esewa',
      'name': 'eSewa',
      'icon': 'assets/icons/esewa.png',
      'color': const Color(0xFF55A64B),
    },
    {
      'id': 'cash',
      'name': 'Cash on Delivery',
      'icon': 'assets/icons/cash.png',
      'color': Colors.grey[700]!,
    },
  ];
});
