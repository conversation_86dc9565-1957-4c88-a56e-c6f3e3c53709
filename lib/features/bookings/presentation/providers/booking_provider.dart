import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/bookings/domain/repositories/booking_repository.dart';

// Provider for BookingRepository
final bookingRepositoryProvider = Provider<BookingRepository>((ref) {
  throw UnimplementedError('BookingRepository not initialized');
});

// Provider for the list of user's bookings
final userBookingsProvider = FutureProvider.autoDispose.family<List<BookingModel>, String>(
  (ref, userId) async {
    final repository = ref.watch(bookingRepositoryProvider);
    final result = await repository.getUserBookings(userId);
    return result.fold(
      (failure) => throw failure,
      (bookings) => bookings,
    );
  },
);

// Provider for the list of owner's bookings
final ownerBookingsProvider = FutureProvider.autoDispose.family<List<BookingModel>, String>(
  (ref, ownerId) async {
    final repository = ref.watch(bookingRepositoryProvider);
    final result = await repository.getOwnerBookings(ownerId);
    return result.fold(
      (failure) => throw failure,
      (bookings) => bookings,
    );
  },
);

// Provider for a single booking
final bookingProvider = FutureProvider.autoDispose.family<BookingModel, String>(
  (ref, id) async {
    final repository = ref.watch(bookingRepositoryProvider);
    final result = await repository.getBooking(id);
    return result.fold(
      (failure) => throw failure,
      (booking) => booking,
    );
  },
);

// Notifier for booking operations
class BookingNotifier extends StateNotifier<AsyncValue<void>> {
  final BookingRepository _repository;
  
  BookingNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<void> createBooking(BookingModel booking) async {
    state = const AsyncValue.loading();
    try {
      final result = await _repository.createBooking(booking);
      state = result.fold(
        (failure) => AsyncValue.error(failure, StackTrace.current),
        (_) => const AsyncValue.data(null),
      );
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> updateBookingStatus(
    String bookingId, 
    String status, {
    String? rejectionReason,
  }) async {
    state = const AsyncValue.loading();
    try {
      final result = await _repository.updateBookingStatus(
        bookingId, 
        status,
        rejectionReason: rejectionReason,
      );
      state = result.fold(
        (failure) => AsyncValue.error(failure, StackTrace.current),
        (_) => const AsyncValue.data(null),
      );
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> cancelBooking(String bookingId, String reason) async {
    state = const AsyncValue.loading();
    try {
      final result = await _repository.cancelBooking(bookingId, reason);
      state = result.fold(
        (failure) => AsyncValue.error(failure, StackTrace.current),
        (_) => const AsyncValue.data(null),
      );
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<bool> checkAvailability(
    String vehicleId, 
    DateTime startDate, 
    DateTime endDate, {
    String? excludeBookingId,
  }) async {
    try {
      final result = await _repository.checkAvailability(
        vehicleId, 
        startDate, 
        endDate,
        excludeBookingId: excludeBookingId,
      );
      return result.fold(
        (failure) => throw failure,
        (isAvailable) => isAvailable,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<double> calculatePrice(
    String vehicleId, 
    DateTime startDate, 
    DateTime endDate,
  ) async {
    try {
      final result = await _repository.calculatePrice(
        vehicleId, 
        startDate, 
        endDate,
      );
      return result.fold(
        (failure) => throw failure,
        (price) => price,
      );
    } catch (e) {
      rethrow;
    }
  }
}

// Provider for BookingNotifier
final bookingNotifierProvider = StateNotifierProvider<BookingNotifier, AsyncValue<void>>((ref) {
  final repository = ref.watch(bookingRepositoryProvider);
  return BookingNotifier(repository);
});
