import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/features/auth/presentation/providers/auth_provider.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/bookings/presentation/providers/booking_provider.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_info_row.dart';
import 'package:gaadi_sewa/features/vehicles/presentation/widgets/vehicle_owner_info.dart';
import 'package:intl/intl.dart';

class BookingFormScreen extends ConsumerStatefulWidget {
  final VehicleModel vehicle;

  const BookingFormScreen({
    Key? key,
    required this.vehicle,
  }) : super(key: key);

  @override
  ConsumerState<BookingFormScreen> createState() => _BookingFormScreenState();
}

class _BookingFormScreenState extends ConsumerState<BookingFormScreen> {
  final _formKey = GlobalKey<FormState>();

  DateTime? _startDate;
  DateTime? _endDate;
  TimeOfDay? _pickupTime;
  int _numDays = 1;
  double _totalPrice = 0;
  bool _isCalculating = false;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    // Set default start date to tomorrow
    _startDate = DateTime.now().add(const Duration(days: 1));
    _pickupTime = const TimeOfDay(hour: 10, minute: 0);
    _calculatePrice();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Book Vehicle'),
        centerTitle: true,
      ),
      body: _isSubmitting
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Vehicle info card
                    _buildVehicleInfo(),

                    const SizedBox(height: 24),

                    // Booking details section
                    const Text(
                      'Booking Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Start date picker
                    _buildDateField(
                      label: 'Start Date',
                      initialDate: _startDate,
                      onDateSelected: (date) {
                        setState(() {
                          _startDate = date;
                          // Reset end date if it's before start date
                          if (_endDate != null && _endDate!.isBefore(date)) {
                            _endDate = null;
                            _totalPrice = 0;
                          }
                          _calculatePrice();
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // End date picker
                    _buildDateField(
                      label: 'End Date',
                      initialDate: _endDate,
                      firstDate: _startDate,
                      onDateSelected: (date) {
                        setState(() {
                          _endDate = date;
                          _calculatePrice();
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Pickup time picker
                    _buildTimeField(
                      label: 'Pickup Time',
                      initialTime: _pickupTime,
                      onTimeSelected: (time) {
                        setState(() {
                          _pickupTime = time;
                        });
                      },
                    ),

                    const SizedBox(height: 24),

                    // Price summary
                    _buildPriceSummary(),

                    const SizedBox(height: 32),

                    // Submit button
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _submitBooking,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Book Now',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildVehicleInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Vehicle title and type
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${widget.vehicle.make} ${widget.vehicle.model} (${widget.vehicle.year})',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    widget.vehicle.type.toString().split('.').last,
                    style: const TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Vehicle details
            VehicleInfoRow(
              icon: Icons.speed,
              label: 'Mileage',
              value: '${widget.vehicle.mileage ?? 'N/A'} kmpl',
            ),
            const SizedBox(height: 8),
            VehicleInfoRow(
              icon: Icons.people,
              label: 'Seats',
              value: '${widget.vehicle.seatingCapacity}',
            ),
            const SizedBox(height: 8),
            VehicleInfoRow(
              icon: Icons.local_gas_station,
              label: 'Fuel Type',
              value: widget.vehicle.fuelType.toString().split('.').last,
            ),

            const SizedBox(height: 12),

            // Owner info
            const Text(
              'Owner',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            VehicleOwnerInfo(ownerId: widget.vehicle.ownerId),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? initialDate,
    required Function(DateTime) onDateSelected,
    DateTime? firstDate,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: initialDate ?? DateTime.now(),
              firstDate: firstDate ?? DateTime.now(),
              lastDate: DateTime.now().add(const Duration(days: 365)),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: const ColorScheme.light(
                      primary: AppColors.primary,
                      onPrimary: Colors.white,
                      onSurface: Colors.black87,
                    ),
                  ),
                  child: child!,
                );
              },
            );

            if (date != null) {
              onDateSelected(date);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  initialDate != null
                      ? DateFormat('MMM d, y').format(initialDate)
                      : 'Select $label',
                  style: TextStyle(
                    color:
                        initialDate != null ? Colors.black87 : Colors.grey[600],
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: Colors.grey[600],
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeField({
    required String label,
    required TimeOfDay? initialTime,
    required Function(TimeOfDay) onTimeSelected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final time = await showTimePicker(
              context: context,
              initialTime: initialTime ?? const TimeOfDay(hour: 10, minute: 0),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: const ColorScheme.light(
                      primary: AppColors.primary,
                      onPrimary: Colors.white,
                      onSurface: Colors.black87,
                    ),
                  ),
                  child: child!,
                );
              },
            );

            if (time != null) {
              onTimeSelected(time);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  initialTime != null
                      ? initialTime.format(context)
                      : 'Select $label',
                  style: TextStyle(
                    color:
                        initialTime != null ? Colors.black87 : Colors.grey[600],
                  ),
                ),
                Icon(
                  Icons.access_time,
                  color: Colors.grey[600],
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPriceSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          const Text(
            'Price Summary',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          // Daily rate
          _buildPriceRow(
            label:
                '${_numDays} ${_numDays > 1 ? 'days' : 'day'} × NPR ${widget.vehicle.dailyRate.toStringAsFixed(2)}/day',
            value:
                'NPR ${(widget.vehicle.dailyRate * _numDays).toStringAsFixed(2)}',
          ),

          // Service fee
          _buildPriceRow(
            label: 'Service fee',
            value: 'NPR 0.00',
          ),

          // Divider
          const Divider(height: 24, thickness: 1),

          // Total price
          _buildPriceRow(
            label: 'Total',
            value: 'NPR ${_totalPrice.toStringAsFixed(2)}',
            isTotal: true,
          ),

          if (_isCalculating) ...[
            const SizedBox(height: 8),
            const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPriceRow({
    required String label,
    required String value,
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isTotal ? Colors.black87 : Colors.grey[700],
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: isTotal ? AppColors.primary : Colors.black87,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _calculatePrice() async {
    if (_startDate == null || _endDate == null) {
      setState(() => _totalPrice = 0);
      return;
    }

    setState(() => _isCalculating = true);

    try {
      final notifier = ref.read(bookingNotifierProvider.notifier);
      final price = await notifier.calculatePrice(
        widget.vehicle.id,
        _startDate!,
        _endDate!,
      );

      if (mounted) {
        setState(() {
          _numDays = _endDate!.difference(_startDate!).inDays + 1;
          _totalPrice = price;
          _isCalculating = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to calculate price: $e')),
        );
        setState(() => _isCalculating = false);
      }
    }
  }

  Future<void> _submitBooking() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    if (_startDate == null || _endDate == null || _pickupTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select all required fields')),
      );
      return;
    }

    // Combine date and time for start date
    final startDateTime = DateTime(
      _startDate!.year,
      _startDate!.month,
      _startDate!.day,
      _pickupTime!.hour,
      _pickupTime!.minute,
    );

    // Check if vehicle is available
    try {
      setState(() => _isSubmitting = true);

      final notifier = ref.read(bookingNotifierProvider.notifier);
      final isAvailable = await notifier.checkAvailability(
        widget.vehicle.id,
        startDateTime,
        _endDate!,
      );

      if (!isAvailable) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Sorry, this vehicle is not available for the selected dates'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() => _isSubmitting = false);
        }
        return;
      }

      // Create booking
      // TODO: Get current user from auth provider
      const user = null; // Replace with actual auth provider
      if (user == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please sign in to book a vehicle')),
          );
          setState(() => _isSubmitting = false);
        }
        return;
      }

      final booking = BookingModel(
        id: '', // Will be generated by Supabase
        userId: user.id,
        vehicleId: widget.vehicle.id,
        vehicle: widget.vehicle,
        startDate: startDateTime,
        endDate: _endDate!,
        totalAmount: _totalPrice,
        status: BookingStatus.pending,
        createdAt: DateTime.now(),
      );

      await notifier.createBooking(booking);

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Booking request sent successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to booking details or bookings list
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to book vehicle: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }
}
