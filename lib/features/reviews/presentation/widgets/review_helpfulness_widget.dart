import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/review_model.dart';

class ReviewHelpfulnessState {
  final Map<String, bool> helpfulVotes; // reviewId -> isHelpful
  final bool isLoading;
  final String? error;

  ReviewHelpfulnessState({
    this.helpfulVotes = const {},
    this.isLoading = false,
    this.error,
  });

  ReviewHelpfulnessState copyWith({
    Map<String, bool>? helpfulVotes,
    bool? isLoading,
    String? error,
  }) {
    return ReviewHelpfulnessState(
      helpfulVotes: helpfulVotes ?? this.helpfulVotes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class ReviewHelpfulnessNotifier extends StateNotifier<ReviewHelpfulnessState> {
  ReviewHelpfulnessNotifier() : super(ReviewHelpfulnessState());

  Future<void> markReviewHelpful(String reviewId, bool isHelpful) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // TODO: Implement API call to save vote to backend
      // For now, we'll just update the local state
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      
      final updatedVotes = Map<String, bool>.from(state.helpfulVotes);
      updatedVotes[reviewId] = isHelpful;
      
      state = state.copyWith(
        helpfulVotes: updatedVotes,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to save your vote: $e',
      );
    }
  }
}

final reviewHelpfulnessProvider = StateNotifierProvider<ReviewHelpfulnessNotifier, ReviewHelpfulnessState>((ref) {
  return ReviewHelpfulnessNotifier();
});

class ReviewHelpfulnessWidget extends ConsumerWidget {
  final ReviewModel review;

  const ReviewHelpfulnessWidget({
    Key? key,
    required this.review,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final helpfulnessState = ref.watch(reviewHelpfulnessProvider);
    final hasVoted = helpfulnessState.helpfulVotes.containsKey(review.id);
    final isHelpful = helpfulnessState.helpfulVotes[review.id] ?? false;
    final theme = Theme.of(context);
    
    // Get helpful count from review metadata or default to 0
    final helpfulCount = (review.metadata?['helpfulCount'] as int?) ?? 0;
    final notHelpfulCount = (review.metadata?['notHelpfulCount'] as int?) ?? 0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        const SizedBox(height: 8),
        Text(
          'Was this review helpful?',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildVoteButton(
              context,
              ref,
              icon: Icons.thumb_up_outlined,
              label: 'Yes${hasVoted && isHelpful ? ' (${helpfulCount + 1})' : helpfulCount > 0 ? ' ($helpfulCount)' : ''}',
              isSelected: hasVoted && isHelpful,
              onPressed: () {
                ref.read(reviewHelpfulnessProvider.notifier).markReviewHelpful(review.id, true);
              },
            ),
            const SizedBox(width: 16),
            _buildVoteButton(
              context,
              ref,
              icon: Icons.thumb_down_outlined,
              label: 'No${hasVoted && !isHelpful ? ' (${notHelpfulCount + 1})' : notHelpfulCount > 0 ? ' ($notHelpfulCount)' : ''}',
              isSelected: hasVoted && !isHelpful,
              onPressed: () {
                ref.read(reviewHelpfulnessProvider.notifier).markReviewHelpful(review.id, false);
              },
            ),
            if (helpfulnessState.isLoading)
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
          ],
        ),
        if (helpfulnessState.error != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              helpfulnessState.error!,
              style: TextStyle(
                color: theme.colorScheme.error,
                fontSize: 12,
              ),
            ),
          ),
        if (hasVoted)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'Thank you for your feedback!',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildVoteButton(
    BuildContext context,
    WidgetRef ref,
    {
      required IconData icon,
      required String label,
      required bool isSelected,
      required VoidCallback onPressed,
    }
  ) {
    final theme = Theme.of(context);
    final helpfulnessState = ref.watch(reviewHelpfulnessProvider);
    
    return OutlinedButton.icon(
      onPressed: helpfulnessState.isLoading ? null : onPressed,
      icon: Icon(
        icon,
        size: 16,
        color: isSelected
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurfaceVariant,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurfaceVariant,
        ),
      ),
      style: OutlinedButton.styleFrom(
        backgroundColor: isSelected
            ? theme.colorScheme.primaryContainer.withOpacity(0.3)
            : Colors.transparent,
        side: BorderSide(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.outline,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
      ),
    );
  }
}
