import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/routes/app_router.dart';
import 'package:gaadi_sewa/features/reviews/domain/models/rating_stats_model.dart';
import 'package:gaadi_sewa/features/reviews/presentation/providers/review_provider.dart';
import 'package:gaadi_sewa/features/reviews/presentation/screens/review_list_screen.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/rating_bar.dart';
import 'package:gaadi_sewa/features/reviews/presentation/widgets/rating_stats_widget.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:go_router/go_router.dart';

class ReviewSummaryWidget extends ConsumerWidget {
  final VehicleModel vehicle;
  final bool showViewAllButton;

  const ReviewSummaryWidget({
    Key? key,
    required this.vehicle,
    this.showViewAllButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ratingStatsState = ref.watch(ratingStatsProvider(vehicle.id));

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Ratings & Reviews',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (showViewAllButton)
                TextButton(
                  onPressed: () {
                    context.pushNamed(
                      ReviewListScreen.routeName,
                      extra: vehicle,
                    );
                  },
                  child: const Text('View All'),
                ),
            ],
          ),
          const SizedBox(height: 8),
          if (ratingStatsState.isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else if (ratingStatsState.failure != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Colors.red[300],
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Failed to load ratings',
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                    ),
                    TextButton(
                      onPressed: () => ref.refresh(ratingStatsProvider(vehicle.id)),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else if (ratingStatsState.stats == null ||
              ratingStatsState.stats!.totalReviews == 0)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.star_border,
                      color: Colors.grey[400],
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No ratings yet',
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            RatingStatsWidget(
              stats: ratingStatsState.stats!,
              compact: true,
            ),
        ],
      ),
    );
  }
}

class ReviewRatingBadge extends ConsumerWidget {
  final String vehicleId;
  final double height;
  final Color? backgroundColor;
  final Color? textColor;

  const ReviewRatingBadge({
    Key? key,
    required this.vehicleId,
    this.height = 24,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ratingStatsState = ref.watch(ratingStatsProvider(vehicleId));

    if (ratingStatsState.isLoading) {
      return const SizedBox.shrink();
    }

    if (ratingStatsState.stats == null || 
        ratingStatsState.stats!.totalReviews == 0) {
      return const SizedBox.shrink();
    }

    final stats = ratingStatsState.stats!;
    final bgColor = backgroundColor ?? Colors.amber.shade600;
    final txtColor = textColor ?? Colors.white;

    return Container(
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            color: txtColor,
            size: height * 0.7,
          ),
          const SizedBox(width: 2),
          Text(
            stats.averageRating.toStringAsFixed(1),
            style: TextStyle(
              color: txtColor,
              fontWeight: FontWeight.bold,
              fontSize: height * 0.5,
            ),
          ),
          Text(
            ' (${stats.totalReviews})',
            style: TextStyle(
              color: txtColor,
              fontSize: height * 0.4,
            ),
          ),
        ],
      ),
    );
  }
}
