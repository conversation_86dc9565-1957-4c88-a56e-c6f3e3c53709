name: gaadi_sewa
description: "GaadiSewa+ - Peer-to-peer vehicle rental platform for urban Nepal"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.8
  flutter_svg: ^1.1.6
  cached_network_image: ^3.3.1
  google_maps_flutter: ^2.5.3
  flutter_map: ^3.1.0
  flutter_rating_bar: ^4.0.1

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.1

  # Supabase
  supabase_flutter: ^2.3.1

  # Authentication
  google_sign_in: ^6.1.14
  flutter_facebook_auth: ^6.0.4

  # Navigation
  go_router: ^13.2.0

  # Utilities
  shared_preferences: ^2.2.2
  url_launcher: ^6.2.2
  geolocator: ^10.1.0
  image_picker: ^1.0.7
  uuid: ^4.2.2
  intl_phone_field: ^3.1.0
  flutter_dotenv: ^5.1.0
  json_annotation: ^4.8.1
  geolocator_platform_interface: ^4.0.7
  google_fonts: ^3.0.1
  equatable: ^2.0.7

  # Network & Async
  connectivity_plus: ^5.0.2
  dartz: ^0.10.1

  # UI Components
  shimmer: ^3.0.0
  flutter_spinkit: ^5.2.0
  table_calendar: ^3.0.9
  flutter_slidable: ^3.0.1
  flutter_animate: ^4.2.0
  intl: ^0.19.0
  timeago: ^3.5.0
  dio: ^5.8.0+1
  injectable: ^2.5.0

  # Additional utilities
  logger: ^2.0.2+1
  image: ^4.1.7
  path_provider: ^2.1.2
  path: ^1.8.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.6
  riverpod_generator: ^2.3.9
  json_serializable: ^6.7.1

  # Testing
  mockito: ^5.4.4
  mocktail: ^1.0.3
  integration_test:
    sdk: flutter

  # Linting
  effective_dart: ^1.3.2
  flutter_native_splash: ^2.3.10

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets configuration
  assets:
    - assets/images/
    - assets/icons/
    - .env

  # Generated files
  generate: true
