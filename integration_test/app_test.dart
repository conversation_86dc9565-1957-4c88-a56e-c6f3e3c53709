import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:gaadi_sewa/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('GaadiSewa+ App Integration Tests', () {
    testWidgets('app should start and show splash screen', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Verify splash screen or initial screen is shown
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('should navigate to vehicle list from home', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Look for navigation elements
      final vehicleListButton = find.text('Browse Vehicles').first;
      if (vehicleListButton.evaluate().isNotEmpty) {
        await tester.tap(vehicleListButton);
        await tester.pumpAndSettle();

        // Verify we're on the vehicle list screen
        expect(find.text('Available Vehicles'), findsWidgets);
      }
    });

    testWidgets('should show login screen when accessing protected features', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Try to access a protected feature
      final profileButton = find.byIcon(Icons.person).first;
      if (profileButton.evaluate().isNotEmpty) {
        await tester.tap(profileButton);
        await tester.pumpAndSettle();

        // Should show login screen or be redirected to login
        expect(
          find.textContaining('Login').or(find.textContaining('Sign In')),
          findsWidgets,
        );
      }
    });

    testWidgets('should be able to search vehicles', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to vehicle list
      final vehicleListButton = find.text('Browse Vehicles').first;
      if (vehicleListButton.evaluate().isNotEmpty) {
        await tester.tap(vehicleListButton);
        await tester.pumpAndSettle();

        // Look for search functionality
        final searchField = find.byType(TextField).first;
        if (searchField.evaluate().isNotEmpty) {
          await tester.enterText(searchField, 'Toyota');
          await tester.pump();

          // Verify search is working (results should update)
          expect(find.byType(TextField), findsWidgets);
        }
      }
    });

    testWidgets('should show vehicle details when vehicle card is tapped', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to vehicle list
      final vehicleListButton = find.text('Browse Vehicles').first;
      if (vehicleListButton.evaluate().isNotEmpty) {
        await tester.tap(vehicleListButton);
        await tester.pumpAndSettle();

        // Wait for vehicles to load and tap on first vehicle card
        await tester.pump(const Duration(seconds: 2));
        
        final vehicleCards = find.byType(Card);
        if (vehicleCards.evaluate().isNotEmpty) {
          await tester.tap(vehicleCards.first);
          await tester.pumpAndSettle();

          // Verify we're on vehicle details screen
          expect(
            find.textContaining('Book').or(find.textContaining('Details')),
            findsWidgets,
          );
        }
      }
    });

    testWidgets('should handle network connectivity issues gracefully', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // The app should handle offline state gracefully
      // This test verifies the app doesn't crash when network is unavailable
      expect(find.byType(MaterialApp), findsOneWidget);
      
      // Look for any error messages or offline indicators
      final errorMessages = find.textContaining('error').or(
        find.textContaining('offline').or(
          find.textContaining('connection')
        )
      );
      
      // App should either work or show appropriate error messages
      // but should not crash
      expect(tester.takeException(), isNull);
    });

    testWidgets('should show appropriate loading states', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to vehicle list
      final vehicleListButton = find.text('Browse Vehicles').first;
      if (vehicleListButton.evaluate().isNotEmpty) {
        await tester.tap(vehicleListButton);
        
        // Check for loading indicators during the initial pump
        await tester.pump(const Duration(milliseconds: 100));
        
        // Should show loading indicators while data is being fetched
        expect(
          find.byType(CircularProgressIndicator).or(
            find.textContaining('Loading')
          ),
          findsWidgets,
        );
        
        await tester.pumpAndSettle();
      }
    });

    testWidgets('should maintain state during navigation', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate through different screens and verify state is maintained
      final vehicleListButton = find.text('Browse Vehicles').first;
      if (vehicleListButton.evaluate().isNotEmpty) {
        await tester.tap(vehicleListButton);
        await tester.pumpAndSettle();

        // Apply a filter or search
        final searchField = find.byType(TextField).first;
        if (searchField.evaluate().isNotEmpty) {
          await tester.enterText(searchField, 'Test');
          await tester.pump();

          // Navigate away and back
          await tester.pageBack();
          await tester.pumpAndSettle();
          
          await tester.tap(vehicleListButton);
          await tester.pumpAndSettle();

          // Verify state is maintained (search text should still be there)
          expect(find.text('Test'), findsWidgets);
        }
      }
    });

    testWidgets('should handle deep linking correctly', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Verify the app starts correctly and can handle navigation
      expect(find.byType(MaterialApp), findsOneWidget);
      
      // The app should be able to handle different routes
      expect(tester.takeException(), isNull);
    });

    testWidgets('should show error states appropriately', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to different screens and verify error handling
      final vehicleListButton = find.text('Browse Vehicles').first;
      if (vehicleListButton.evaluate().isNotEmpty) {
        await tester.tap(vehicleListButton);
        await tester.pumpAndSettle();

        // The app should handle errors gracefully without crashing
        expect(tester.takeException(), isNull);
        
        // Look for appropriate error messages if any
        final errorStates = find.textContaining('Error').or(
          find.textContaining('Failed').or(
            find.textContaining('Try again')
          )
        );
        
        // If there are errors, they should be user-friendly
        if (errorStates.evaluate().isNotEmpty) {
          expect(find.byType(ElevatedButton).or(find.byType(TextButton)), findsWidgets);
        }
      }
    });

    testWidgets('should be responsive to different screen sizes', (tester) async {
      // Test with different screen sizes
      await tester.binding.setSurfaceSize(const Size(800, 600)); // Tablet size
      
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Verify the app adapts to larger screen
      expect(find.byType(MaterialApp), findsOneWidget);
      
      // Test with mobile size
      await tester.binding.setSurfaceSize(const Size(375, 667)); // iPhone size
      await tester.pump();
      
      // App should still work on mobile
      expect(find.byType(MaterialApp), findsOneWidget);
      
      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });
  });
}
